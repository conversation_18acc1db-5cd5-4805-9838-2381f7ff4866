# Oracle Error Handling Vulnerability

## Description

В протоколе Malda обнаружена критическая уязвимость в обработке ошибок оракулов. Существует несоответствие между документированным поведением интерфейса IOracleOperator и фактической реализацией оракулов, что может привести к полной блокировке критических функций протокола.

## Vulnerability Details

### Проблема архитектуры

1. **Интерфейс IOracleOperator** документирует, что функции должны возвращать `0` при недоступности цены:

```solidity
// src/interfaces/IOracleOperator.sol
/**
 * @return The underlying asset price mantissa (scaled by 1e18).
 *  Zero means the price is unavailable.
 */
function getUnderlyingPrice(address mToken) external view returns (uint256);
```

2. **Operator ожидает возврат 0** и проверяет это условие:

```solidity
// src/Operator/Operator.sol
function _convertMarketAmountToUSDValue(uint256 amount, address mToken) internal view returns (uint256) {
    uint256 oraclePriceMantissa = IOracleOperator(oracleOperator).getUnderlyingPrice(mToken);
    require(oraclePriceMantissa != 0, Operator_OracleUnderlyingFetchError());
    // ...
}
```

3. **Но все оракулы используют revert/require** вместо возврата 0:

**MixedPriceOracleV3:**
```solidity
function _getLatestPrice(string memory symbol, IDefaultAdapter.PriceConfig memory config) {
    if (config.defaultFeed == address(0)) revert("missing priceFeed");
    require(price > 0, MixedPriceOracle_InvalidPrice());
    require(block.timestamp - updatedAt < _getStaleness(symbol), MixedPriceOracle_StalePrice());
}
```

**ChainlinkOracle:**
```solidity
function _getLatestPrice(string memory symbol) internal view returns (uint256, uint256) {
    require(address(priceFeeds[symbol]) != address(0), ChainlinkOracle_NoPriceFeed());
    require(price > 0, ChainlinkOracle_ZeroPrice());
}
```

### Затронутые функции

**Критические функции Operator, которые будут заблокированы:**

1. **Ликвидации** - `liquidateCalculateSeizeTokens()`:
```solidity
uint256 priceBorrowedMantissa = IOracleOperator(oracleOperator).getUnderlyingPrice(mTokenBorrowed);
uint256 priceCollateralMantissa = IOracleOperator(oracleOperator).getUnderlyingPrice(mTokenCollateral);
require(priceBorrowedMantissa > 0 && priceCollateralMantissa > 0, Operator_PriceFetchFailed());
```

2. **Проверка ликвидности** - `_getHypotheticalAccountLiquidity()`:
```solidity
vars.oraclePriceMantissa = IOracleOperator(oracleOperator).getUnderlyingPrice(_asset);
require(vars.oraclePriceMantissa != 0, Operator_OracleUnderlyingFetchError());
```

3. **Займы** - `beforeMTokenBorrow()` (использует `_getHypotheticalAccountLiquidity()`)

4. **Redeem операции** - `_beforeRedeem()` (использует `_getHypotheticalAccountLiquidity()`)

5. **Outflow limits** - `checkOutflowVolumeLimit()` (использует `_convertMarketAmountToUSDValue()`)

6. **Установка collateral factor** - `setCollateralFactor()`:
```solidity
if (newCollateralFactorMantissa != 0 && IOracleOperator(oracleOperator).getUnderlyingPrice(mToken) == 0) {
    revert Operator_EmptyPrice();
}
```

## Impact

**Severity: High**

### Сценарии блокировки протокола

1. **Stale Price Data**: Когда данные оракула устаревают, все критические операции становятся недоступными
2. **Missing Price Feed**: При отсутствии или неправильной настройке feed'а протокол полностью блокируется
3. **Invalid Price (≤0)**: Отрицательные или нулевые цены от внешних источников блокируют все операции
4. **Oracle Downtime**: Любые проблемы с внешними оракулами парализуют протокол

### Последствия

- **Полная блокировка ликвидаций** → невозможность поддержания здоровья протокола
- **Блокировка всех займов** → пользователи не могут занимать средства
- **Блокировка redeem операций** → пользователи не могут выводить свои средства
- **Блокировка outflow limits** → нарушение механизмов защиты
- **Невозможность управления** → админы не могут обновлять параметры рынков

## Recommended Mitigation

### Решение 1: Try-Catch в оракулах

Модифицировать все оракулы для использования `try-catch` и возврата `0` при ошибках:

```solidity
function getUnderlyingPrice(address mToken) external view override returns (uint256) {
    try this._getUnderlyingPriceInternal(mToken) returns (uint256 price) {
        return price;
    } catch {
        return 0; // Return 0 on any error as per interface documentation
    }
}

function _getUnderlyingPriceInternal(address mToken) external view returns (uint256) {
    // Current implementation logic here
    string memory symbol = ImTokenMinimal(ImTokenMinimal(mToken).underlying()).symbol();
    PriceConfig memory config = configs[symbol];
    uint256 priceUsd = _getPriceUSD(symbol);
    return priceUsd * 10 ** (18 - config.underlyingDecimals);
}
```

### Решение 2: Graceful degradation в Operator

Добавить fallback механизмы в критические функции Operator для обработки недоступности цен:

```solidity
function _getUnderlyingPriceSafe(address mToken) internal view returns (uint256, bool) {
    try IOracleOperator(oracleOperator).getUnderlyingPrice(mToken) returns (uint256 price) {
        return (price, price > 0);
    } catch {
        return (0, false);
    }
}
```

## Comparison with Compound

В оригинальном Compound и его форках оракулы следуют принципу "fail gracefully" - возвращают `0` при ошибках, что позволяет протоколу продолжать работать с ограниченной функциональностью. В Malda же любая проблема с оракулом приводит к полной блокировке критических функций, что противоречит принципам надежной DeFi архитектуры.

## References

- Canto Protocol Oracle Bug: Similar issue where `getUnderlyingPrice()` reverted instead of returning 0
- Compound V2 Oracle Interface: Standard for returning 0 on price unavailability
- Malda IOracleOperator Interface: Documents zero return for unavailable prices but implementations don't follow this
